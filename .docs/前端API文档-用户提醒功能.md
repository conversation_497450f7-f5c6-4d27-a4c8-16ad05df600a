# 用户提醒功能 - 前端API文档

## 📋 功能概述

用户提醒功能允许用户设置四种类型的提醒：
- 💰 **发薪日提醒**：设置发薪日期和提前提醒天数
- 🎭 **开演时间提醒**：设置演出开始前的提醒时间
- 🎫 **开票时间提醒**：设置开票前的提醒时间  
- 💳 **看剧基金提醒**：管理看剧资金目标和进度提醒

---

## 🔗 API接口列表

### 1. 获取用户提醒设置

**接口信息**
```
GET /userReminder/getUserReminder
```

**请求示例**
```javascript
// 使用fetch
const response = await fetch('/userReminder/getUserReminder', {
  method: 'GET',
  headers: {
    'Authorization': `Bearer ${token}`,
    'Content-Type': 'application/json'
  }
});
const data = await response.json();

// 使用axios
const { data } = await axios.get('/userReminder/getUserReminder');
```

**响应数据结构**
```typescript
interface UserReminderResponse {
  id: number;
  userId: number;
  salaryDayReminder: {
    enabled: 0 | 1;           // 0:关闭 1:开启
    salaryDay: number;        // 发薪日 1-31
    advanceDays: number;      // 提前提醒天数
  };
  showTimeReminder: {
    enabled: 0 | 1;           // 0:关闭 1:开启
    advanceHours: number;     // 提前提醒小时数
  };
  ticketSaleReminder: {
    enabled: 0 | 1;           // 0:关闭 1:开启
    advanceHours: number;     // 提前提醒小时数
  };
  theaterFundReminder: {
    enabled: 0 | 1;           // 0:关闭 1:开启
    currentAmount: number;    // 当前金额
    targetAmount: number;     // 目标金额
    reminderThreshold: number; // 提醒阈值 0.1-1.0
    progressPercentage: number; // 进度百分比
    reachedThreshold: boolean;  // 是否达到阈值
  };
  status: 0 | 1;
  createTime: string;
  updateTime: string;
  remark?: string;
}
```

**响应示例**
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "id": 1,
    "userId": 123,
    "salaryDayReminder": {
      "enabled": 1,
      "salaryDay": 15,
      "advanceDays": 1
    },
    "showTimeReminder": {
      "enabled": 1,
      "advanceHours": 2
    },
    "ticketSaleReminder": {
      "enabled": 1,
      "advanceHours": 24
    },
    "theaterFundReminder": {
      "enabled": 1,
      "currentAmount": 500.00,
      "targetAmount": 1000.00,
      "reminderThreshold": 0.80,
      "progressPercentage": 50.00,
      "reachedThreshold": false
    },
    "status": 1,
    "createTime": "2025-01-03 10:00:00",
    "updateTime": "2025-01-03 10:00:00"
  }
}
```

---

### 2. 保存/更新用户提醒设置

**接口信息**
```
POST /userReminder/saveOrUpdateUserReminder
```

**请求参数类型**
```typescript
interface UserReminderRequest {
  // 发薪日提醒
  salaryDayEnabled?: 0 | 1;
  salaryDay?: number;              // 1-31
  salaryDayAdvanceDays?: number;   // 0-30

  // 开演时间提醒  
  showTimeEnabled?: 0 | 1;
  showTimeAdvanceHours?: number;   // 0-168 (7天)

  // 开票时间提醒
  ticketSaleEnabled?: 0 | 1;
  ticketSaleAdvanceHours?: number; // 0-720 (30天)

  // 看剧基金提醒
  theaterFundEnabled?: 0 | 1;
  theaterFundCurrentAmount?: number;    // 0.00-999999.99
  theaterFundTargetAmount?: number;     // 0.00-999999.99
  theaterFundReminderThreshold?: number; // 0.10-1.00

  remark?: string;
}
```

**请求示例**
```javascript
// 完整设置示例
const requestData = {
  // 发薪日提醒：开启，15号发薪，提前1天提醒
  salaryDayEnabled: 1,
  salaryDay: 15,
  salaryDayAdvanceDays: 1,
  
  // 开演提醒：开启，提前2小时
  showTimeEnabled: 1,
  showTimeAdvanceHours: 2,
  
  // 开票提醒：开启，提前24小时
  ticketSaleEnabled: 1,
  ticketSaleAdvanceHours: 24,
  
  // 看剧基金：开启，当前500元，目标1000元，80%提醒
  theaterFundEnabled: 1,
  theaterFundCurrentAmount: 500.00,
  theaterFundTargetAmount: 1000.00,
  theaterFundReminderThreshold: 0.80,
  
  remark: "我的提醒设置"
};

const response = await fetch('/userReminder/saveOrUpdateUserReminder', {
  method: 'POST',
  headers: {
    'Authorization': `Bearer ${token}`,
    'Content-Type': 'application/json'
  },
  body: JSON.stringify(requestData)
});
```

**响应示例**
```json
{
  "code": 200,
  "msg": "操作成功"
}
```

---

### 3. 删除用户提醒设置

**接口信息**
```
DELETE /userReminder/deleteUserReminder
```

**请求示例**
```javascript
const response = await fetch('/userReminder/deleteUserReminder', {
  method: 'DELETE',
  headers: {
    'Authorization': `Bearer ${token}`
  }
});
```

**响应示例**
```json
{
  "code": 200,
  "msg": "操作成功"
}
```

---

### 4. 更新看剧基金金额

**接口信息**
```
POST /userReminder/updateTheaterFund
```

**请求参数**
- `currentAmount` (必填): 当前金额
- `targetAmount` (可选): 目标金额

**请求示例**
```javascript
// 只更新当前金额
const response1 = await fetch('/userReminder/updateTheaterFund?currentAmount=600.00', {
  method: 'POST',
  headers: {
    'Authorization': `Bearer ${token}`
  }
});

// 同时更新当前金额和目标金额
const response2 = await fetch('/userReminder/updateTheaterFund?currentAmount=600.00&targetAmount=1200.00', {
  method: 'POST',
  headers: {
    'Authorization': `Bearer ${token}`
  }
});

// 使用URLSearchParams
const params = new URLSearchParams({
  currentAmount: '600.00',
  targetAmount: '1200.00'
});

const response3 = await fetch(`/userReminder/updateTheaterFund?${params}`, {
  method: 'POST',
  headers: {
    'Authorization': `Bearer ${token}`
  }
});
```

---

## 🎨 前端实现建议

### React Hook 示例

```typescript
import { useState, useEffect } from 'react';

interface UseUserReminder {
  data: UserReminderResponse | null;
  loading: boolean;
  error: string | null;
  refresh: () => Promise<void>;
  save: (data: UserReminderRequest) => Promise<boolean>;
  updateFund: (current: number, target?: number) => Promise<boolean>;
}

export const useUserReminder = (): UseUserReminder => {
  const [data, setData] = useState<UserReminderResponse | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const refresh = async () => {
    setLoading(true);
    try {
      const response = await fetch('/userReminder/getUserReminder');
      const result = await response.json();
      if (result.code === 200) {
        setData(result.data);
        setError(null);
      } else {
        setError(result.msg);
      }
    } catch (err) {
      setError('网络错误');
    } finally {
      setLoading(false);
    }
  };

  const save = async (requestData: UserReminderRequest): Promise<boolean> => {
    try {
      const response = await fetch('/userReminder/saveOrUpdateUserReminder', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(requestData)
      });
      const result = await response.json();
      if (result.code === 200) {
        await refresh(); // 刷新数据
        return true;
      } else {
        setError(result.msg);
        return false;
      }
    } catch (err) {
      setError('保存失败');
      return false;
    }
  };

  const updateFund = async (current: number, target?: number): Promise<boolean> => {
    try {
      const params = new URLSearchParams({ currentAmount: current.toString() });
      if (target !== undefined) {
        params.append('targetAmount', target.toString());
      }
      
      const response = await fetch(`/userReminder/updateTheaterFund?${params}`, {
        method: 'POST'
      });
      const result = await response.json();
      if (result.code === 200) {
        await refresh();
        return true;
      } else {
        setError(result.msg);
        return false;
      }
    } catch (err) {
      setError('更新失败');
      return false;
    }
  };

  useEffect(() => {
    refresh();
  }, []);

  return { data, loading, error, refresh, save, updateFund };
};
```

### Vue Composition API 示例

```typescript
import { ref, onMounted } from 'vue';

export const useUserReminder = () => {
  const data = ref<UserReminderResponse | null>(null);
  const loading = ref(false);
  const error = ref<string | null>(null);

  const refresh = async () => {
    loading.value = true;
    try {
      const response = await fetch('/userReminder/getUserReminder');
      const result = await response.json();
      if (result.code === 200) {
        data.value = result.data;
        error.value = null;
      } else {
        error.value = result.msg;
      }
    } catch (err) {
      error.value = '网络错误';
    } finally {
      loading.value = false;
    }
  };

  const save = async (requestData: UserReminderRequest): Promise<boolean> => {
    try {
      const response = await fetch('/userReminder/saveOrUpdateUserReminder', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(requestData)
      });
      const result = await response.json();
      if (result.code === 200) {
        await refresh();
        return true;
      } else {
        error.value = result.msg;
        return false;
      }
    } catch (err) {
      error.value = '保存失败';
      return false;
    }
  };

  onMounted(() => {
    refresh();
  });

  return {
    data: readonly(data),
    loading: readonly(loading),
    error: readonly(error),
    refresh,
    save
  };
};
```

---

## ⚠️ 注意事项

### 参数验证规则
- `salaryDay`: 1-31之间的整数
- `salaryDayAdvanceDays`: 0-30之间的整数
- `showTimeAdvanceHours`: 0-168之间的整数（最多7天）
- `ticketSaleAdvanceHours`: 0-720之间的整数（最多30天）
- `theaterFundCurrentAmount/targetAmount`: 0.00-999999.99
- `theaterFundReminderThreshold`: 0.10-1.00（10%-100%）

### 错误处理
```javascript
const handleApiCall = async (apiCall) => {
  try {
    const response = await apiCall();
    const result = await response.json();
    
    if (result.code === 200) {
      return { success: true, data: result.data };
    } else {
      return { success: false, error: result.msg };
    }
  } catch (error) {
    return { success: false, error: '网络请求失败' };
  }
};
```

### 状态码说明
- `200`: 操作成功
- `500`: 服务器错误
- 具体错误信息在 `msg` 字段中返回

---

## 🚀 快速开始

1. **获取用户设置**：页面加载时调用获取接口
2. **表单绑定**：将返回数据绑定到表单控件
3. **保存设置**：表单提交时调用保存接口
4. **实时更新**：看剧基金可以单独更新，无需完整表单

这份文档涵盖了前端开发所需的所有信息，包括接口定义、TypeScript类型、实际代码示例和最佳实践建议。
