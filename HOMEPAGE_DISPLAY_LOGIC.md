# 首页显示逻辑修正总结

## 🎯 修正内容

### 主要倒计时区域（大卡片）
- **显示内容**：开演倒计时
- **显示格式**：X天X时X分X秒（实时更新）
- **数据来源**：设置的开演时间
- **空状态**：未设置开演时间时显示"暂无提醒 - 点击设置提醒"

### 小项目网格显示

#### 1. 发薪项目
- **标签**：发薪
- **显示内容**：距离下次发薪日还有X天
- **计算逻辑**：
  - 如果当前日期 < 发薪日：显示本月发薪日倒计时
  - 如果当前日期 >= 发薪日：显示下月发薪日倒计时
- **数据来源**：设置的发薪日（每月几号）

#### 2. 开票项目  
- **标签**：开票
- **显示内容**：距离开票时间还有X天
- **计算逻辑**：设置的开票时间 - 当前时间
- **数据来源**：设置的开票时间

#### 3. 今天赚了项目
- **标签**：今天赚了
- **显示内容**：¥X,XXX（看剧基金当前金额）
- **数据来源**：看剧基金当前金额

## 🔧 技术实现

### 主要倒计时计算
```typescript
// 主要倒计时数据 - 固定显示开演倒计时
const mainCountdownData = computed(() => {
  // 查找开演倒计时数据
  const showTimeReminder = countdownData.value?.main?.type === 'showTime' ? 
    countdownData.value.main : 
    countdownData.value?.others?.find((item: any) => item.type === 'showTime')
  
  if (showTimeReminder && showTimeReminder.targetTime) {
    const realTimeCountdown = calculateRealTimeCountdown(showTimeReminder.targetTime)
    if (realTimeCountdown) {
      return {
        title: '开演倒计时',
        days: realTimeCountdown.days,
        hours: realTimeCountdown.hours,
        minutes: realTimeCountdown.minutes,
        seconds: realTimeCountdown.seconds,
        hasDetailedTime: true
      }
    }
  }
  
  // 没有设置开演时间时返回null，显示空状态
  return null
})
```

### 项目数据计算
```typescript
const projectsData = computed(() => {
  const projects: any = {}
  
  if (!countdownData.value) return projects

  // 收集所有提醒数据（包括main和others）
  const allReminders = []
  if (countdownData.value.main) {
    allReminders.push(countdownData.value.main)
  }
  if (countdownData.value.others) {
    allReminders.push(...countdownData.value.others)
  }

  // 处理每种类型的提醒
  allReminders.forEach((item: any) => {
    if (item.type === 'salary') {
      // 发薪：显示到下次发薪日的天数
      projects.salary = { value: item.days, unit: '天', enabled: true }
    } else if (item.type === 'ticketSale') {
      // 开票：显示到开票时间的天数
      projects.ticketTime = { value: item.days, unit: '天', enabled: true }
    } else if (item.type === 'fund') {
      // 今天赚了：显示看剧基金当前金额
      projects.fund = { value: item.value, unit: '', enabled: true }
    }
  })

  return projects
})
```

### 实时倒计时计算
```typescript
// 计算实时倒计时
const calculateRealTimeCountdown = (targetTime: string) => {
  const target = dayjs(targetTime)
  const now = dayjs(currentTime.value)
  const diff = target.diff(now)
  
  if (diff <= 0) return null
  
  const days = Math.floor(diff / (1000 * 60 * 60 * 24))
  const hours = Math.floor((diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60))
  const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60))
  const seconds = Math.floor((diff % (1000 * 60)) / 1000)
  
  return { days, hours, minutes, seconds }
}
```

## 📱 显示效果

### 主要倒计时区域
```
┌─────────────────────────────────────┐
│           开演倒计时                │
│                                     │
│         15天  8时                   │
│         30分  45秒                  │
│                                     │
│                            ⚙️       │
└─────────────────────────────────────┘
```

### 小项目网格
```
┌─────────┬─────────┬─────────┐
│  发薪   │  开票   │今天赚了 │
│  15天   │  3天    │ ¥1,280  │
└─────────┴─────────┴─────────┘
```

### 空状态显示
```
┌─────────────────────────────────────┐
│           暂无提醒                  │
│                                     │
│           🕐                        │
│       点击设置提醒                  │
│                                     │
│                            ⚙️       │
└─────────────────────────────────────┘
```

## 🎯 用户体验

### 主要倒计时
- **实时更新**：每秒刷新，显示精确的倒计时
- **清晰显示**：天时分秒分两行显示，易于阅读
- **空状态引导**：未设置时提供清晰的操作引导

### 小项目显示
- **发薪倒计时**：智能计算下次发薪日，跨月自动处理
- **开票倒计时**：显示到开票时间的剩余天数
- **基金显示**：直观显示当前存款金额

### 数据一致性
- **设置同步**：设置页面的修改立即反映到首页
- **实时计算**：所有倒计时都基于当前时间实时计算
- **智能排序**：按紧急程度自动排序显示

## ✅ 修正完成

1. ✅ **主要倒计时**：固定显示开演倒计时（天时分秒）
2. ✅ **发薪项目**：显示到下次发薪日的天数
3. ✅ **开票项目**：显示到开票时间的天数
4. ✅ **今天赚了**：显示看剧基金当前金额
5. ✅ **实时更新**：所有倒计时每秒自动刷新
6. ✅ **空状态处理**：未设置时显示友好提示

现在首页的显示逻辑完全符合需求，用户可以清晰地看到各种提醒信息！
