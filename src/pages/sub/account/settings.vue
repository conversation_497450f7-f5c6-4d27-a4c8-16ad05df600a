<template>
  <view class="page-container">
    <!-- 导航栏 -->
    <u-navbar class="w-full shrink-0" title="提醒小助手" :titleStyle="titleStyle" @leftClick="handleBack" bgColor="transparent" leftIconColor="#1a1a1a" placeholder />

    <!-- 滚动容器 -->
    <scroll-view class="scroll-container" scroll-y enable-back-to-top>
      <view class="content-container">
        <!-- 提醒设置列表 -->
        <view class="settings-list">
          <!-- 发薪日设置 -->
          <view class="setting-card">
            <view class="setting-main">
              <view class="setting-header">
                <view class="setting-info">
                  <view class="setting-icon">
                    <text class="fas fa-dollar-sign"></text>
                  </view>
                  <view class="setting-text">
                    <text class="setting-title">发薪日提醒</text>
                    <text class="setting-description">设置每月发薪日期</text>
                  </view>
                </view>
              </view>

              <view class="setting-config">
                <view class="salary-config">
                  <text class="config-label">每月</text>
                  <view class="input-wrapper">
                    <u-input v-model="reminderSettings.salaryDay" type="number" placeholder="25" class="salary-input" border="none" @change="handleSalaryDayChange" />
                  </view>
                  <text class="config-label">号发薪</text>
                </view>
              </view>
            </view>
          </view>

          <!-- 开演时间设置 -->
          <view class="setting-card">
            <view class="setting-main">
              <view class="setting-header">
                <view class="setting-info">
                  <view class="setting-icon">
                    <text class="fas fa-theater-masks"></text>
                  </view>
                  <view class="setting-text">
                    <text class="setting-title">开演时间提醒</text>
                    <text class="setting-description">设置即将观看的演出时间</text>
                  </view>
                </view>
              </view>

              <view class="setting-config">
                <view class="datetime-picker" @tap="showDateTimePickerHandler('showTime')">
                  <text class="datetime-text">
                    {{ reminderSettings.showTime ? formatDateTime(reminderSettings.showTime) : '点击选择演出时间' }}
                  </text>
                </view>
              </view>
            </view>
          </view>

          <!-- 开票时间设置 -->
          <view class="setting-card">
            <view class="setting-main">
              <view class="setting-header">
                <view class="setting-info">
                  <view class="setting-icon">
                    <text class="fas fa-ticket-alt"></text>
                  </view>
                  <view class="setting-text">
                    <text class="setting-title">开票时间提醒</text>
                    <text class="setting-description">设置关注演出的开票时间</text>
                  </view>
                </view>
              </view>

              <view class="setting-config">
                <view class="datetime-picker" @tap="showDateTimePickerHandler('ticketSaleTime')">
                  <text class="datetime-text">
                    {{ reminderSettings.ticketSaleTime ? formatDateTime(reminderSettings.ticketSaleTime) : '点击选择开票时间' }}
                  </text>
                </view>
              </view>
            </view>
          </view>

          <!-- 看剧基金设置 -->
          <view class="setting-card">
            <view class="setting-main">
              <view class="setting-header">
                <view class="setting-info">
                  <view class="setting-icon">
                    <text class="fas fa-piggy-bank"></text>
                  </view>
                  <view class="setting-text">
                    <text class="setting-title">看剧基金</text>
                    <text class="setting-description">管理您的观剧预算</text>
                  </view>
                </view>
              </view>

              <view class="setting-config">
                <view class="fund-config">
                  <view class="fund-input-group">
                    <text class="fund-label">当前金额</text>
                    <view class="fund-input-wrapper">
                      <text class="currency-symbol">¥</text>
                      <u-input v-model="reminderSettings.theaterFundCurrentAmount" type="number" placeholder="0" class="fund-input" border="none" @change="handleFundChange" />
                    </view>
                  </view>
                  <view class="fund-input-group">
                    <text class="fund-label">目标金额</text>
                    <view class="fund-input-wrapper">
                      <text class="currency-symbol">¥</text>
                      <u-input v-model="reminderSettings.theaterFundTargetAmount" type="number" placeholder="2000" class="fund-input" border="none" @change="handleFundChange" />
                    </view>
                  </view>
                  <!-- 进度显示 -->
                  <view class="progress-section">
                    <view class="progress-header">
                      <text class="progress-label">完成进度</text>
                      <text class="progress-value">{{ fundProgress }}%</text>
                    </view>
                    <view class="progress-bar">
                      <view class="progress-fill" :style="{ width: fundProgress + '%' }"></view>
                    </view>
                  </view>
                </view>
              </view>
            </view>
          </view>
        </view>

        <!-- 使用说明 -->
        <view class="help-card">
          <view class="help-header">
            <view class="help-icon">
              <text class="fas fa-info-circle"></text>
            </view>
            <text class="help-title">使用说明</text>
          </view>
          <view class="help-content">
            <view class="help-item">
              <text class="help-bullet">•</text>
              <text class="help-text">最紧急的倒计时将在主页大卡片中显示</text>
            </view>
            <view class="help-item">
              <text class="help-bullet">•</text>
              <text class="help-text">其他启用的倒计时在小网格中显示</text>
            </view>
            <view class="help-item">
              <text class="help-bullet">•</text>
              <text class="help-text">倒计时按距离天数自动排序优先级</text>
            </view>
          </view>
        </view>

        <!-- 底部操作提示 -->
        <view class="bottom-tip">
          <text class="tip-text">设置完成后，返回主页查看倒计时显示</text>
        </view>
      </view>
    </scroll-view>

    <!-- toast提示 -->
    <u-toast ref="uToast"></u-toast>

    <!-- 时间选择器 -->
    <u-datetime-picker :show="showDateTimePicker" :value="getCurrentPickerValue()" mode="datetime" @confirm="confirmDateTime" @cancel="showDateTimePicker = false" />
  </view>
</template>

<script lang="ts" setup>
import type { UserReminderRequest } from '@/api/userReminder'
import { useUserReminder } from '@/composables/useUserReminder'
import { onLoad } from '@dcloudio/uni-app'
import dayjs from 'dayjs'
import { computed, onMounted, ref, watch } from 'vue'

const uToast = ref()
const showDateTimePicker = ref(false)
const currentPickerKey = ref('')

// 用户提醒功能
const { data: reminderData, loading: reminderLoading, refresh: refreshReminder, save: saveReminder } = useUserReminder()

// 提醒设置数据
const reminderSettings = ref({
  salaryDay: 25,
  showTime: '',
  ticketSaleTime: '',
  theaterFundCurrentAmount: 0,
  theaterFundTargetAmount: 2000
})

// 监听API数据变化，更新本地设置
watch(
  reminderData,
  newData => {
    if (newData) {
      reminderSettings.value = {
        salaryDay: newData.salaryDay || 25,
        showTime: newData.showTime || '',
        ticketSaleTime: newData.ticketSaleTime || '',
        theaterFundCurrentAmount: newData.theaterFundCurrentAmount || 0,
        theaterFundTargetAmount: newData.theaterFundTargetAmount || 2000
      }
    }
  },
  { immediate: true }
)

// 标题样式
const titleStyle = {
  color: '#1a1a1a',
  fontSize: '36rpx',
  fontWeight: '600'
}

// 基金进度计算
const fundProgress = computed(() => {
  const current = Number(reminderSettings.value.theaterFundCurrentAmount) || 0
  const target = Number(reminderSettings.value.theaterFundTargetAmount) || 2000
  if (target === 0) return 0
  return Math.min(Math.round((current / target) * 100), 100)
})

onMounted(() => {
  uni.getSystemInfo({
    success: res => {
      console.log('系统信息:', res)
    }
  })
})

/* 返回处理 */
const handleBack = () => {
  uni.navigateBack({
    delta: 1,
    fail: () => {
      uni.reLaunch({
        url: '/pages/homepage/index2'
      })
    }
  })
}

/* 显示时间选择器 */
const showDateTimePickerHandler = (key: string) => {
  currentPickerKey.value = key
  showDateTimePicker.value = true
}

onLoad(() => {
  loadSettings()
})

/* 加载设置 */
const loadSettings = async () => {
  await refreshReminder()
}

/* 发薪日变化处理 */
const handleSalaryDayChange = async () => {
  const requestData: UserReminderRequest = {
    salaryDay: Number(reminderSettings.value.salaryDay) || 25
  }

  const success = await saveReminder(requestData)
  if (!success) {
    uni.showToast({
      title: '保存失败，请重试',
      icon: 'none',
      duration: 2000
    })
  }
}

/* 看剧基金变化处理 */
const handleFundChange = async () => {
  const requestData: UserReminderRequest = {
    theaterFundCurrentAmount: Number(reminderSettings.value.theaterFundCurrentAmount) || 0,
    theaterFundTargetAmount: Number(reminderSettings.value.theaterFundTargetAmount) || 2000
  }

  const success = await saveReminder(requestData)
  if (!success) {
    uni.showToast({
      title: '保存失败，请重试',
      icon: 'none',
      duration: 2000
    })
  }
}

/* 确认选择时间 */
const confirmDateTime = async (e: any) => {
  if (currentPickerKey.value) {
    const requestData: UserReminderRequest = {}

    if (currentPickerKey.value === 'showTime') {
      reminderSettings.value.showTime = e.value
      requestData.showTime = e.value
    } else if (currentPickerKey.value === 'ticketSaleTime') {
      reminderSettings.value.ticketSaleTime = e.value
      requestData.ticketSaleTime = e.value
    }

    const success = await saveReminder(requestData)
    if (!success) {
      uni.showToast({
        title: '保存失败，请重试',
        icon: 'none',
        duration: 2000
      })
    }
  }
  showDateTimePicker.value = false
  currentPickerKey.value = ''
}

/* 获取当前选择器的值 */
const getCurrentPickerValue = () => {
  if (!currentPickerKey.value) return ''

  if (currentPickerKey.value === 'showTime') {
    return reminderSettings.value.showTime || ''
  } else if (currentPickerKey.value === 'ticketSaleTime') {
    return reminderSettings.value.ticketSaleTime || ''
  }

  return ''
}

/* 格式化日期时间 */
const formatDateTime = (datetime: string | undefined) => {
  if (!datetime) return ''
  return dayjs(datetime).format('MM月DD日 HH:mm')
}
</script>

<style lang="scss" scoped>
@import '@/uni_modules/uv-ui-tools/libs/css/variable.scss';

.page-container {
  height: 100vh;
  background: linear-gradient(180deg, #fafafa 0%, #f5f5f5 100%);
  position: relative;
  display: flex;
  flex-direction: column;
}

/* 滚动容器 */
.scroll-container {
  flex: 1;
  height: auto;
}

.content-container {
  padding: 0 24rpx 40rpx;
  margin-bottom: 200rpx;
}

/* 设置列表 */
.settings-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.setting-card {
  background: #ffffff;
  border-radius: 24rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 16rpx rgba(0, 0, 0, 0.04);
  border: 1px solid #f0f0f0;
}

.setting-main {
  padding: 32rpx;
}

.setting-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 24rpx;
}

.setting-info {
  display: flex;
  align-items: center;
  flex: 1;
}

.setting-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 56rpx;
  height: 56rpx;
  background: linear-gradient(135deg, #9333ea 0%, #764ba2 100%);
  border-radius: 14rpx;
  margin-right: 20rpx;

  .fas {
    font-size: 28rpx;
    color: #ffffff;
  }
}

.setting-text {
  flex: 1;
}

.setting-title {
  display: block;
  font-size: 30rpx;
  font-weight: 600;
  color: #1a1a1a;
  line-height: 38rpx;
  margin-bottom: 4rpx;
}

.setting-description {
  display: block;
  font-size: 24rpx;
  color: #666666;
  line-height: 32rpx;
}

/* 配置区域 */
.setting-config {
  background: #f8f9fa;
  border-radius: 16rpx;
  padding: 24rpx;
  margin-top: 8rpx;
}

.config-section {
  width: 100%;
}

/* 发薪日配置 */
.salary-config {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 16rpx;
}

.config-label {
  font-size: 28rpx;
  color: #1a1a1a;
  font-weight: 500;
}

.input-wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 120rpx;
  height: 64rpx;
  background: #ffffff;
  border-radius: 12rpx;
  border: 1px solid #e5e7eb;
}

.salary-input {
  text-align: center;
  font-size: 28rpx;
  font-weight: 600;
  color: #1a1a1a;
}

/* 日期时间选择器 */
.datetime-picker {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 64rpx;
  background: #ffffff;
  border-radius: 12rpx;
  border: 1px solid #e5e7eb;
  transition: all 0.3s ease;

  &:active {
    transform: scale(0.98);
    border-color: #9333ea;
  }
}

.datetime-text {
  font-size: 28rpx;
  font-weight: 500;
  color: #1a1a1a;
}

/* 基金配置 */
.fund-config {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.fund-input-group {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.fund-label {
  font-size: 26rpx;
  color: #1a1a1a;
  font-weight: 500;
  width: 140rpx;
  flex-shrink: 0;
}

.fund-input-wrapper {
  display: flex;
  align-items: center;
  flex: 1;
  height: 64rpx;
  background: #ffffff;
  border-radius: 12rpx;
  border: 1px solid #e5e7eb;
  padding: 0 16rpx;
}

.currency-symbol {
  font-size: 24rpx;
  color: #666666;
  margin-right: 8rpx;
}

.fund-input {
  flex: 1;
  font-size: 26rpx;
  font-weight: 500;
  color: #1a1a1a;
}

/* 进度条 */
.progress-section {
  margin-top: 8rpx;
}

.progress-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 12rpx;
}

.progress-label {
  font-size: 24rpx;
  color: #666666;
}

.progress-value {
  font-size: 24rpx;
  font-weight: 600;
  color: #9333ea;
}

.progress-bar {
  width: 100%;
  height: 8rpx;
  background: #e5e7eb;
  border-radius: 4rpx;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #9333ea 0%, #764ba2 100%);
  border-radius: 4rpx;
  transition: width 0.3s ease;
}

/* 帮助卡片 */
.help-card {
  background: #ffffff;
  border-radius: 20rpx;
  padding: 24rpx;
  margin-top: 40rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.03);
  border: 1px solid #f0f0f0;
}

.help-header {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}

.help-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32rpx;
  height: 32rpx;
  margin-right: 12rpx;

  .fas {
    font-size: 24rpx;
    color: #9333ea;
  }
}

.help-title {
  font-size: 26rpx;
  font-weight: 600;
  color: #1a1a1a;
}

.help-content {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.help-item {
  display: flex;
  align-items: flex-start;
  line-height: 32rpx;
}

.help-bullet {
  font-size: 22rpx;
  color: #9333ea;
  margin-right: 12rpx;
  margin-top: 2rpx;
  flex-shrink: 0;
}

.help-text {
  font-size: 22rpx;
  color: #666666;
  line-height: 32rpx;
  flex: 1;
}

/* 底部提示 */
.bottom-tip {
  text-align: center;
  margin-top: 24rpx;
  margin-bottom: 24rpx;
}

.tip-text {
  font-size: 24rpx;
  color: #999999;
  line-height: 34rpx;
}

/* 交互效果 */
.setting-card {
  transition: all 0.3s ease;

  &:active {
    transform: scale(0.98);
  }
}

/* 深度样式覆盖 */
:deep(.u-input__content__field-wrapper__field) {
  text-align: center;
}
</style>
