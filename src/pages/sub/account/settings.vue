<template>
  <view class="page-container">
    <!-- 导航栏 -->
    <u-navbar class="w-full shrink-0" title="提醒小助手" :titleStyle="titleStyle" @leftClick="handleBack" bgColor="transparent" leftIconColor="#1a1a1a" placeholder />

    <!-- 滚动容器 -->
    <scroll-view class="scroll-container" scroll-y enable-back-to-top>
      <view class="content-container">
        <!-- 倒计时项目列表 -->
        <view class="settings-list">
          <view class="setting-card" v-for="type in countdownTypes" :key="type.key">
            <!-- 主要设置区域 -->
            <view class="setting-main">
              <!-- 标题和开关 -->
              <view class="setting-header">
                <view class="setting-info">
                  <view class="setting-icon">
                    <text class="fas" :class="type.iconClass"></text>
                  </view>
                  <view class="setting-text">
                    <text class="setting-title">{{ type.title }}</text>
                    <text class="setting-description">{{ type.description }}</text>
                  </view>
                </view>
                <u-switch :value="countdownSettings[type.key].enabled" @change="val => handleSwitchToggle(type.key, val)" activeColor="#9333ea" inactiveColor="rgba(0,0,0,0.1)" size="28" />
              </view>

              <!-- 详细配置区域 -->
              <view v-if="countdownSettings[type.key].enabled" class="setting-config">
                <!-- 发薪日设置 -->
                <view v-if="type.type === 'salary'" class="config-section">
                  <view class="salary-config">
                    <text class="config-label">每月</text>
                    <view class="input-wrapper">
                      <u-input
                        :value="countdownSettings[type.key].dayOfMonth"
                        type="number"
                        placeholder="25"
                        class="salary-input"
                        :border="false"
                        @change="val => handleInputChange('salary', 'dayOfMonth', val)" />
                    </view>
                    <text class="config-label">号发薪</text>
                  </view>
                </view>

                <!-- 日期时间设置 -->
                <view v-if="type.type === 'datetime'" class="config-section">
                  <view class="datetime-picker" @tap="showDateTimePickerHandler(type.key)">
                    <text class="datetime-text">
                      {{ countdownSettings[type.key].datetime ? formatDateTime(countdownSettings[type.key].datetime) : '点击选择时间' }}
                    </text>
                  </view>
                </view>

                <!-- 看剧基金设置 -->
                <view v-if="type.type === 'fund'" class="config-section">
                  <view class="fund-config">
                    <view class="fund-input-group">
                      <text class="fund-label">当前金额</text>
                      <view class="fund-input-wrapper">
                        <text class="currency-symbol">¥</text>
                        <u-input
                          :value="countdownSettings[type.key].current"
                          type="number"
                          placeholder="0"
                          class="fund-input"
                          :border="false"
                          @change="val => handleInputChange('fund', 'current', val)" />
                      </view>
                    </view>
                    <view class="fund-input-group">
                      <text class="fund-label">目标金额</text>
                      <view class="fund-input-wrapper">
                        <text class="currency-symbol">¥</text>
                        <u-input
                          :value="countdownSettings[type.key].target"
                          type="number"
                          placeholder="2000"
                          class="fund-input"
                          :border="false"
                          @change="val => handleInputChange('fund', 'target', val)" />
                      </view>
                    </view>
                    <!-- 进度显示 -->
                    <view class="progress-section">
                      <view class="progress-header">
                        <text class="progress-label">完成进度</text>
                        <text class="progress-value">{{ fundProgress }}%</text>
                      </view>
                      <view class="progress-bar">
                        <view class="progress-fill" :style="{ width: fundProgress + '%' }"></view>
                      </view>
                    </view>
                  </view>
                </view>
              </view>
            </view>
          </view>
        </view>

        <!-- 使用说明 -->
        <view class="help-card">
          <view class="help-header">
            <view class="help-icon">
              <text class="fas fa-info-circle"></text>
            </view>
            <text class="help-title">使用说明</text>
          </view>
          <view class="help-content">
            <view class="help-item">
              <text class="help-bullet">•</text>
              <text class="help-text">最紧急的倒计时将在主页大卡片中显示</text>
            </view>
            <view class="help-item">
              <text class="help-bullet">•</text>
              <text class="help-text">其他启用的倒计时在小网格中显示</text>
            </view>
            <view class="help-item">
              <text class="help-bullet">•</text>
              <text class="help-text">倒计时按距离天数自动排序优先级</text>
            </view>
          </view>
        </view>

        <!-- 底部操作提示 -->
        <view class="bottom-tip">
          <text class="tip-text">设置完成后，返回主页查看倒计时显示</text>
        </view>
      </view>
    </scroll-view>

    <!-- toast提示 -->
    <u-toast ref="uToast"></u-toast>

    <!-- 时间选择器 -->
    <u-datetime-picker
      :show="showDateTimePicker"
      :value="currentPickerKey ? countdownSettings[currentPickerKey]?.datetime || '' : ''"
      mode="datetime"
      @confirm="confirmDateTime"
      @cancel="showDateTimePicker = false" />
  </view>
</template>

<script lang="ts" setup>
import type { UserReminderRequest } from '@/api/userReminder'
import { useUserReminder } from '@/composables/useUserReminder'
import { onLoad } from '@dcloudio/uni-app'
import { computed, onMounted, ref } from 'vue'

const uToast = ref()
const showDateTimePicker = ref(false)
const currentPickerKey = ref('')

// 用户提醒功能
const { data: reminderData, loading: reminderLoading, refresh: refreshReminder, save: saveReminder } = useUserReminder()

// 标题样式
const titleStyle = {
  color: '#1a1a1a',
  fontSize: '36rpx',
  fontWeight: '600'
}

onMounted(() => {
  uni.getSystemInfo({
    success: res => {
      console.log('系统信息:', res)
    }
  })
})

/* 返回处理 */
const handleBack = () => {
  // 简单的返回处理，直接使用 navigateBack
  uni.navigateBack({
    delta: 1,
    fail: () => {
      // 如果返回失败，跳转到首页
      uni.reLaunch({
        url: '/pages/homepage/index2'
      })
    }
  })
}

/* 显示时间选择器 */
const showDateTimePickerHandler = (key: string) => {
  currentPickerKey.value = key
  showDateTimePicker.value = true
}

// 倒计时类型配置
const countdownTypes = ref([
  {
    key: 'salary',
    title: '发薪日倒计时',
    description: '设置每月发薪日期，提醒您工资到账时间',
    iconClass: 'fa-dollar-sign',
    type: 'salary'
  },
  {
    key: 'showTime',
    title: '演出倒计时',
    description: '设置即将观看的演出时间',
    iconClass: 'fa-clock',
    type: 'datetime'
  },
  {
    key: 'ticketTime',
    title: '开票提醒',
    description: '设置关注演出的开票时间，不错过抢票',
    iconClass: 'fa-ticket-alt',
    type: 'datetime'
  },
  {
    key: 'fund',
    title: '看剧基金',
    description: '设置看剧基金目标，管理您的观剧预算',
    iconClass: 'fa-piggy-bank',
    type: 'fund'
  }
])

// 倒计时设置类型定义
interface CountdownSettings {
  [key: string]: {
    enabled: boolean
    dayOfMonth?: number
    datetime?: string
    current?: number
    target?: number
    advanceHours?: number
    advanceDays?: number
  }
}

// 倒计时设置 - 基于API数据
const countdownSettings = computed<CountdownSettings>({
  get() {
    if (!reminderData.value) {
      return {
        salary: { enabled: false, dayOfMonth: 25, advanceDays: 1 },
        showTime: { enabled: false, datetime: '', advanceHours: 2 },
        ticketTime: { enabled: false, datetime: '', advanceHours: 24 },
        fund: { enabled: false, current: 0, target: 2000 }
      }
    }

    const data = reminderData.value
    return {
      salary: {
        enabled: !!data.salaryDayReminder.enabled,
        dayOfMonth: data.salaryDayReminder.salaryDay,
        advanceDays: data.salaryDayReminder.advanceDays
      },
      showTime: {
        enabled: !!data.showTimeReminder.enabled,
        datetime: '', // 这里需要从其他地方获取具体的演出时间
        advanceHours: data.showTimeReminder.advanceHours
      },
      ticketTime: {
        enabled: !!data.ticketSaleReminder.enabled,
        datetime: '', // 这里需要从其他地方获取具体的开票时间
        advanceHours: data.ticketSaleReminder.advanceHours
      },
      fund: {
        enabled: !!data.theaterFundReminder.enabled,
        current: data.theaterFundReminder.currentAmount,
        target: data.theaterFundReminder.targetAmount
      }
    }
  },
  set(newValue) {
    // 这里暂时不处理，通过saveSettings函数来保存
  }
})

// 基金进度计算
const fundProgress = computed(() => {
  if (!reminderData.value?.theaterFundReminder) return 0
  return Math.round(reminderData.value.theaterFundReminder.progressPercentage) || 0
})

onLoad(() => {
  loadSettings()
})

/* 加载设置 */
const loadSettings = async () => {
  await refreshReminder()
}

/* 开关切换处理 */
const handleSwitchToggle = async (key: string, enabled: boolean) => {
  if (!reminderData.value) return

  // 构建请求数据
  const requestData: UserReminderRequest = {}

  if (key === 'salary') {
    requestData.salaryDayEnabled = enabled ? 1 : 0
    requestData.salaryDay = countdownSettings.value.salary.dayOfMonth || 25
    requestData.salaryDayAdvanceDays = countdownSettings.value.salary.advanceDays || 1
  } else if (key === 'showTime') {
    requestData.showTimeEnabled = enabled ? 1 : 0
    requestData.showTimeAdvanceHours = countdownSettings.value.showTime.advanceHours || 2
  } else if (key === 'ticketTime') {
    requestData.ticketSaleEnabled = enabled ? 1 : 0
    requestData.ticketSaleAdvanceHours = countdownSettings.value.ticketTime.advanceHours || 24
  } else if (key === 'fund') {
    requestData.theaterFundEnabled = enabled ? 1 : 0
    requestData.theaterFundCurrentAmount = countdownSettings.value.fund.current || 0
    requestData.theaterFundTargetAmount = countdownSettings.value.fund.target || 2000
    requestData.theaterFundReminderThreshold = 0.8
  }

  const success = await saveReminder(requestData)
  if (success) {
    uni.showToast({
      title: enabled ? '已开启' : '已关闭',
      icon: 'none',
      duration: 1500
    })
  } else {
    uni.showToast({
      title: '操作失败，请重试',
      icon: 'none',
      duration: 2000
    })
  }
}

/* 输入框变化处理 */
const handleInputChange = async (key: string, field: string, value: any) => {
  if (!reminderData.value) return

  const requestData: UserReminderRequest = {}

  if (key === 'salary' && field === 'dayOfMonth') {
    requestData.salaryDayEnabled = countdownSettings.value.salary.enabled ? 1 : 0
    requestData.salaryDay = Number(value) || 25
    requestData.salaryDayAdvanceDays = countdownSettings.value.salary.advanceDays || 1
  } else if (key === 'fund') {
    requestData.theaterFundEnabled = countdownSettings.value.fund.enabled ? 1 : 0
    if (field === 'current') {
      requestData.theaterFundCurrentAmount = Number(value) || 0
      requestData.theaterFundTargetAmount = countdownSettings.value.fund.target || 2000
    } else if (field === 'target') {
      requestData.theaterFundCurrentAmount = countdownSettings.value.fund.current || 0
      requestData.theaterFundTargetAmount = Number(value) || 2000
    }
    requestData.theaterFundReminderThreshold = 0.8
  }

  await saveReminder(requestData)
}

/* 保存设置 */
const saveSettings = async () => {
  if (!reminderData.value) return

  const settings = countdownSettings.value
  const requestData: UserReminderRequest = {
    // 发薪日提醒
    salaryDayEnabled: settings.salary.enabled ? 1 : 0,
    salaryDay: settings.salary.dayOfMonth || 25,
    salaryDayAdvanceDays: settings.salary.advanceDays || 1,

    // 开演时间提醒
    showTimeEnabled: settings.showTime.enabled ? 1 : 0,
    showTimeAdvanceHours: settings.showTime.advanceHours || 2,

    // 开票时间提醒
    ticketSaleEnabled: settings.ticketTime.enabled ? 1 : 0,
    ticketSaleAdvanceHours: settings.ticketTime.advanceHours || 24,

    // 看剧基金提醒
    theaterFundEnabled: settings.fund.enabled ? 1 : 0,
    theaterFundCurrentAmount: settings.fund.current || 0,
    theaterFundTargetAmount: settings.fund.target || 2000,
    theaterFundReminderThreshold: 0.8 // 默认80%提醒阈值
  }

  const success = await saveReminder(requestData)
  if (!success) {
    uni.showToast({
      title: '保存失败，请重试',
      icon: 'none',
      duration: 2000
    })
  }
}

/* 确认选择时间 */
const confirmDateTime = (e: any) => {
  if (currentPickerKey.value) {
    countdownSettings.value[currentPickerKey.value].datetime = e.value
    saveSettings()
  }
  showDateTimePicker.value = false
  currentPickerKey.value = ''
}

/* 格式化日期时间 */
const formatDateTime = (datetime: string | undefined) => {
  if (!datetime) return ''
  const date = new Date(datetime)
  return (
    date
      .toLocaleString('zh-CN', {
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
      })
      .replace(/\//g, '月')
      .replace(' ', ' ') + '分'
  )
}
</script>

<style lang="scss" scoped>
@import '@/uni_modules/uv-ui-tools/libs/css/variable.scss';

.page-container {
  height: 100vh;
  background: linear-gradient(180deg, #fafafa 0%, #f5f5f5 100%);
  position: relative;
  display: flex;
  flex-direction: column;
}

/* 滚动容器 */
.scroll-container {
  flex: 1;
  height: auto;
}

.content-container {
  padding: 0 24rpx 40rpx;
  margin-bottom: 200rpx;
}

/* 设置列表 */
.settings-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.setting-card {
  background: #ffffff;
  border-radius: 24rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 16rpx rgba(0, 0, 0, 0.04);
  border: 1px solid #f0f0f0;
}

.setting-main {
  padding: 32rpx;
}

.setting-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 24rpx;
}

.setting-info {
  display: flex;
  align-items: center;
  flex: 1;
}

.setting-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 56rpx;
  height: 56rpx;
  background: linear-gradient(135deg, #9333ea 0%, #764ba2 100%);
  border-radius: 14rpx;
  margin-right: 20rpx;

  .fas {
    font-size: 28rpx;
    color: #ffffff;
  }
}

.setting-text {
  flex: 1;
}

.setting-title {
  display: block;
  font-size: 30rpx;
  font-weight: 600;
  color: #1a1a1a;
  line-height: 38rpx;
  margin-bottom: 4rpx;
}

.setting-description {
  display: block;
  font-size: 24rpx;
  color: #666666;
  line-height: 32rpx;
}

/* 配置区域 */
.setting-config {
  background: #f8f9fa;
  border-radius: 16rpx;
  padding: 24rpx;
  margin-top: 8rpx;
}

.config-section {
  width: 100%;
}

/* 发薪日配置 */
.salary-config {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 16rpx;
}

.config-label {
  font-size: 28rpx;
  color: #1a1a1a;
  font-weight: 500;
}

.input-wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 120rpx;
  height: 64rpx;
  background: #ffffff;
  border-radius: 12rpx;
  border: 1px solid #e5e7eb;
}

.salary-input {
  text-align: center;
  font-size: 28rpx;
  font-weight: 600;
  color: #1a1a1a;
}

/* 日期时间选择器 */
.datetime-picker {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 64rpx;
  background: #ffffff;
  border-radius: 12rpx;
  border: 1px solid #e5e7eb;
  transition: all 0.3s ease;

  &:active {
    transform: scale(0.98);
    border-color: #9333ea;
  }
}

.datetime-text {
  font-size: 28rpx;
  font-weight: 500;
  color: #1a1a1a;
}

/* 基金配置 */
.fund-config {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.fund-input-group {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.fund-label {
  font-size: 26rpx;
  color: #1a1a1a;
  font-weight: 500;
  width: 140rpx;
  flex-shrink: 0;
}

.fund-input-wrapper {
  display: flex;
  align-items: center;
  flex: 1;
  height: 64rpx;
  background: #ffffff;
  border-radius: 12rpx;
  border: 1px solid #e5e7eb;
  padding: 0 16rpx;
}

.currency-symbol {
  font-size: 24rpx;
  color: #666666;
  margin-right: 8rpx;
}

.fund-input {
  flex: 1;
  font-size: 26rpx;
  font-weight: 500;
  color: #1a1a1a;
}

/* 进度条 */
.progress-section {
  margin-top: 8rpx;
}

.progress-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 12rpx;
}

.progress-label {
  font-size: 24rpx;
  color: #666666;
}

.progress-value {
  font-size: 24rpx;
  font-weight: 600;
  color: #9333ea;
}

.progress-bar {
  width: 100%;
  height: 8rpx;
  background: #e5e7eb;
  border-radius: 4rpx;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #9333ea 0%, #764ba2 100%);
  border-radius: 4rpx;
  transition: width 0.3s ease;
}

/* 帮助卡片 */
.help-card {
  background: #ffffff;
  border-radius: 20rpx;
  padding: 24rpx;
  margin-top: 40rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.03);
  border: 1px solid #f0f0f0;
}

.help-header {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}

.help-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32rpx;
  height: 32rpx;
  margin-right: 12rpx;

  .fas {
    font-size: 24rpx;
    color: #9333ea;
  }
}

.help-title {
  font-size: 26rpx;
  font-weight: 600;
  color: #1a1a1a;
}

.help-content {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.help-item {
  display: flex;
  align-items: flex-start;
  line-height: 32rpx;
}

.help-bullet {
  font-size: 22rpx;
  color: #9333ea;
  margin-right: 12rpx;
  margin-top: 2rpx;
  flex-shrink: 0;
}

.help-text {
  font-size: 22rpx;
  color: #666666;
  line-height: 32rpx;
  flex: 1;
}

/* 底部提示 */
.bottom-tip {
  text-align: center;
  margin-top: 24rpx;
  margin-bottom: 24rpx;
}

.tip-text {
  font-size: 24rpx;
  color: #999999;
  line-height: 34rpx;
}

/* 交互效果 */
.setting-card {
  transition: all 0.3s ease;

  &:active {
    transform: scale(0.98);
  }
}

/* 深度样式覆盖 */
:deep(.u-input__content__field-wrapper__field) {
  text-align: center;
}
</style>
