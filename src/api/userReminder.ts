/* 用户提醒功能 API */

/* 获取用户提醒设置 */
export const $getUserReminder = () => uni.$u.http.get('/userReminder/getUserReminder')

/* 保存/更新用户提醒设置 */
export const $saveOrUpdateUserReminder = (data: any) => uni.$u.http.post('/userReminder/saveOrUpdateUserReminder', data)

/* 删除用户提醒设置 */
export const $deleteUserReminder = () => uni.$u.http.delete('/userReminder/deleteUserReminder')

/* 更新看剧基金金额 */
export const $updateTheaterFund = (params: any) => uni.$u.http.post('/userReminder/updateTheaterFund', {}, { params })

// TypeScript 类型定义
export interface UserReminderResponse {
  id: number
  userId: number
  salaryDayReminder: {
    enabled: 0 | 1
    salaryDay: number
    advanceDays: number
  }
  showTimeReminder: {
    enabled: 0 | 1
    advanceHours: number
  }
  ticketSaleReminder: {
    enabled: 0 | 1
    advanceHours: number
  }
  theaterFundReminder: {
    enabled: 0 | 1
    currentAmount: number
    targetAmount: number
    reminderThreshold: number
    progressPercentage: number
    reachedThreshold: boolean
  }
  status: 0 | 1
  createTime: string
  updateTime: string
  remark?: string
}

export interface UserReminderRequest {
  // 发薪日提醒
  salaryDayEnabled?: 0 | 1
  salaryDay?: number
  salaryDayAdvanceDays?: number

  // 开演时间提醒  
  showTimeEnabled?: 0 | 1
  showTimeAdvanceHours?: number

  // 开票时间提醒
  ticketSaleEnabled?: 0 | 1
  ticketSaleAdvanceHours?: number

  // 看剧基金提醒
  theaterFundEnabled?: 0 | 1
  theaterFundCurrentAmount?: number
  theaterFundTargetAmount?: number
  theaterFundReminderThreshold?: number

  remark?: string
}
