# 用户提醒功能实现总结

## 完成的功能

### 1. API 接口集成
- ✅ 创建了 `src/api/userReminder.ts` 文件，包含所有用户提醒相关的API接口
- ✅ 实现了获取、保存、更新、删除用户提醒设置的接口
- ✅ 包含完整的TypeScript类型定义

### 2. 数据管理 Composable
- ✅ 创建了 `src/composables/useUserReminder.ts` 
- ✅ 提供了统一的数据管理和状态处理
- ✅ 实现了倒计时数据的计算逻辑
- ✅ 支持错误处理和加载状态管理

### 3. 首页倒计时显示
- ✅ 更新了 `src/pages/homepage/index2.vue`
- ✅ 集成了真实的API数据显示
- ✅ 实现了主要倒计时和次要项目的动态显示
- ✅ 支持下拉刷新更新提醒数据

### 4. 设置页面功能
- ✅ 更新了 `src/pages/sub/account/settings.vue`
- ✅ 实现了四种提醒类型的设置：
  - 发薪日提醒（每月几号发薪）
  - 开演时间提醒（提前几小时提醒）
  - 开票时间提醒（提前几小时提醒）
  - 看剧基金提醒（当前金额/目标金额）
- ✅ 实现了实时保存功能
- ✅ 支持开关切换和数值修改
- ✅ 显示看剧基金进度条

## 技术实现细节

### API 调用方式
```typescript
// 获取用户提醒设置
const { data } = await $getUserReminder()

// 保存设置
await $saveOrUpdateUserReminder({
  salaryDayEnabled: 1,
  salaryDay: 25,
  salaryDayAdvanceDays: 1
})
```

### 数据流
1. 页面加载时调用 `refreshReminder()` 获取最新数据
2. 用户修改设置时调用 `saveReminder()` 保存到服务器
3. 首页通过 `countdownData` 计算属性显示倒计时信息

### 倒计时计算逻辑
- 发薪日：计算到下次发薪日的天数
- 看剧基金：显示当前金额和进度百分比
- 按优先级排序显示（天数越少优先级越高）

## 文件结构
```
src/
├── api/
│   └── userReminder.ts          # API接口定义
├── composables/
│   └── useUserReminder.ts       # 数据管理逻辑
├── pages/
│   ├── homepage/
│   │   └── index2.vue          # 首页倒计时显示
│   └── sub/account/
│       └── settings.vue        # 设置页面
```

## 使用方法

### 在首页查看倒计时
1. 打开首页，倒计时卡片会显示最紧急的提醒
2. 小网格显示其他启用的提醒项目
3. 下拉刷新可更新数据

### 在设置页面配置提醒
1. 点击首页倒计时卡片的设置按钮
2. 或直接访问 `/pages/sub/account/settings`
3. 开启/关闭各种提醒类型
4. 设置具体的数值（发薪日、金额等）
5. 修改会自动保存到服务器

## 注意事项
- 所有数据都通过API与服务器同步
- 支持实时保存，无需手动保存按钮
- 错误处理已集成，会显示相应的提示信息
- 响应式设计，适配不同屏幕尺寸
